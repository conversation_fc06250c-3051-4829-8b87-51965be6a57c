import { useState } from 'react';
import DearAuPairLetterStep from './dearAuPairLetterStep';
import FamilyPicturesStep from './familyPicturesStep';
import WeeklyScheduleStep from './weeklyScheduleStep';

export default function ProfileStep() {
  const [letterContent, setLetterContent] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [letterCompleted, setLetterCompleted] = useState(false);
  const [picturesCompleted, setPicturesCompleted] = useState(false);
  const [scheduleCompleted, setScheduleCompleted] = useState(false);

  const handleLetterSubmit = (content: string) => {
    setLetterContent(content);
    setLetterCompleted(true);
    console.log('Dear Au-Pair letter submitted:', content);
  };

  const handlePicturesSubmit = (files: File[]) => {
    setUploadedFiles(files);
    setPicturesCompleted(true);
    console.log('Family pictures submitted:', files);
  };

  const handleScheduleSubmit = () => {
    setScheduleCompleted(true);
    console.log('Weekly schedule submitted');
  };

  return (
    <div className="space-y-8">
      <DearAuPairLetterStep
        onSubmit={handleLetterSubmit}
        initialContent={letterContent}
        isCompleted={letterCompleted}
      />

      <FamilyPicturesStep
        onSubmit={handlePicturesSubmit}
        uploadedFiles={uploadedFiles}
        isCompleted={picturesCompleted}
      />

      <WeeklyScheduleStep
        onSubmit={handleScheduleSubmit}
        isCompleted={scheduleCompleted}
      />
    </div>
  );
}
